#include <bits/stdc++.h>
#define endl '\n'
using namespace std;

const int maxn = 1001000;
bool isprime[maxn+5];
vector<int> primes;

void sieve(int limit = maxn) 
{
    memset(isprime, true, sizeof(isprime));
    isprime[0] = isprime[1] = false;
    for (int i = 2; i * i <= limit; i++) {
        if (isprime[i]) {
            for (int j = i * i; j <= limit; j += i)
                isprime[j] = false;
        }
    }
    for (int i = 2; i <= limit; i++) {
        if (isprime[i]) primes.push_back(i);
    }
}

void init()
{
    ios_base::sync_with_stdio(0);
    cin.tie(0);
    cout.tie(0);
}

int main()
{
    init();
    sieve();
    int n;
    cin >> n;
    vector<int> a(n+1);
    for (int i = 1; i <= n; i++) cin >> a[i];

    long long ans = 0;
    for (int i = 1; i <= n; i++) {
        if (!isprime[a[i]]) {
            int target = a[i];
            auto it = lower_bound(primes.begin(), primes.end(), target);
            int diff = INT_MAX;

            if (it != primes.end()) diff = min(diff, abs(*it - target));
            if (it != primes.begin()) {
                --it;
                diff = min(diff, abs(*it - target));
            }
            ans += diff;
        }
    }
    cout << ans;
    return 0;
}
