#include <bits/stdc++.h>
#define endl '\n'
/*                                   ======================                                       
                                ======++*###%%%%%%%%%%%%###*++======                                
                            =====+##%%###*+=*%%%+::*%%%%%%####%%##+=====                            
                         ====*%%%%%%%%=..-=*#%%%=..*%%%%%*. ..:#%%%%%%*====                         
                      ===+##%%%+..=#%%*..=*++#%%=..#%%%%#- .**-+%%%#=+#%##+===                      
                   ===+#%%#=:-##=..+%%#: .-=*#%%=..#%%%%%#-..:*#%%#: ..:=#%%#+===                   
                 ===*#%%%%%*. -+-. .*%#= .*#**%%=..===*#+-##: :#%#-..*%#+#%%%%#*===                 
               ===##%#**#%%%+..:*#: :#%*...:-+#%*****+*%*:....+%#-..-..=%%%%%%%%##===               
             ===*##=.:==*%%%#=..+#*::+%##%%%###******###%%####%%=..+%###%%%%%#+-=##*===             
           ===*#%%+.=#%%%#**%#=.:*%%%##*=--::...    ...::--=*##%#*=:.=#%%#*=:..:#%%%#*===           
          ==+#%%%%=.:#%%%%-:#%%%%#*=-:.                      .:-=*#%###*:..:-..*%%%%%%#+==          
         ==*%%%%%%#:..=*#=.=%%#*=:..                            ..:=*###+##=. -#%%%%%%%%*==         
       ===#%%%%%%%%#+:...-*##+-.                      .             .-+#%%#- :#%%%%%%%%%%#===       
      ===#%%%%%%%%%%%%%%%%*=:.  .:+*+-..           .-##:              .:=*%#=+%%%%%%%%%%%%#===      
     ===%%%%%%%%%%%%%%%%#=:.   :*#%%%%%=         .-=*#%%#*-.            .:=#%%%%%%%%%%%%%%%%===     
    ===#%%%%%%%%%%%%%%#+-.    .=#%*:-*%%-.      .=*#%%%%%%%%%#=.          .-+#%%%%%%%%%%%%%%#===    
    ==#%%%%%%%%%%%%%%#=.      .=#%#++*%#::        =#%%%%#*#%%%#-..          .=#%%%%%%%%%%%%%%#==    
   ==#%%%%%%%%%%%%%%#=.         -#%%#%*-:.    =--*#%%%%%%%###*::.:           .=#%%%%%%%%%%%%%%#==   
  ==+%%%%%%%%%%%%%%*-.          .-*##-::..    -*##%%%%%%%%+::.+**..:-:        .-*%%%%%%%%%%%%%%+==  
  ==#%#+-=--=#%%%%#=.           .*###*-.      .=*#%%%%%%%%%%-...-####%#+.      .=#%%%%#=--=-+#%#==  
 ==*%#-+**=-=-+%%#=.            .**##%%%*-:...=+#%%%%%%%*:::::  -.:-+#%%=.      .=#%%+-=-=**+-#%*== 
 ==#%=-**+==++-##+:             .*##%%%%%%%#+++#%%%%%%%%*:.. .   ..-+#%#=:       :+##=++==+**-=%#== 
==+%%*:**+=+++-%#-.             ..=#%%%%%%%%#++#%%%%%%%%##+*=   .:+*#%#-:.       .-#%-+++=+**:*%%+==
==#%%%*--==--+%%+:                :**:::+#%%###%%%%%%%%%%%%%+:.:+*#%*-::..        :+%%+-===--*%%%#==
==%%%%%%%%#%%%%#=.                :**:.:*#%#=*%%%%%%%%%%#+=:::-+#%+:::.           .=#%%%%#%%%%%%%%==
=+%%%%%%%%%%%%%*-.                :**:. .::-+++*#%%%%%%%#-.  .+##*%%%%%*.         .-#%%%%%%%%%%%%%+=
=+%%%%%%%%%%%%%*-.                :**:. :=++*#%%%%%%%%%%%*:. .+*%%#*++#%*.        .-*%%%%%%%%%%%%%+=
=+%%%%%%%%%%%%%*-                 :**-=+**#%%%%%%%%%%%%%%%-.  .+*#+:::+#-:.        -*%%%%%%%%%%%%%+=
=+%%%%%%%%%%%%%*-.              .+####%%%%%%%%%%##%%#*#%%%+.    -+#=..-::.        .-*%%%%%%%%%%%%%+=
=+%%%%%%%%%%%%%*-.              +*%%%%%%%%%%%%*-:-%%#+*#%%*:.   .:+#*....         .-*%%%%%%%%%%%%%+=
==%%%%%%*++*#%%#-.              .-*#%%*===#*=:::-=+++*#%%%#+.     =*%+..          .=#%%%*+++#%%%%%==
==#%%%+-:***==*%+:                :**=::.....:=++**#%%%%%%%%*-...-+*%*:.          :+%#+:***=:-#%%#==
==+%%++*+:.:**=##-.               :**::.   .=+*#%%%%%%%%%%%+####*##%#-:.         .-#%=**=:.=**=#%+==
 ==#%=--:+*:=*+*#+:               :**:.   .=*#%%%%%%%%%%%%%%+.:----:::..         :+##-**:+*:---##== 
 ==*%*-*+=-+*+=%%#=.              :**:.   :+*%%%%%#+=-+*#%%%%#=...:..           .=#%%%-**+-+**-#*== 
  ==#%#+=--+=*%%%%#=.            .+*#=.    .=+#%%+::...=+*###%%%#=...          .=#%%%%#+=+:==#%#==  
  ==+%%%%%%%%%%%%%%*-.           .=##-:.    -+#%%#-.    .-=+*#%%+::.          .-*%%%%%%%%%%%%%%+==  
   ==#%%%%%%%%%%%%%%#=.           +*#-..   .+#%%%#=-..    .=*#%#::.          .=#%%%%%%%%%%%%%%#==   
    ==#%%%%%%%%%%%%%%#=.           .:-=---=*#%%%*::..     .+#%%+:.          .=#%%%%%%%%%%%%%%#==    
    ===#%%%%%%%%%%%%%%#+-.          +#%%%%%%%%%%-:.  .=*####%%%+:.        .-+#%%%%%%%%%%%%%%#===    
     ==+%%%%%%%%%%%%%%%%#=:.        -####%##%#*-:.   =%%%%%%%%%+:.      .:=#%%%%%%%%%%%%%%%%+==     
      ==+#%%%%%%%%%%%%%%%%*=:.      .:::+::-=::...   .+-:++:==:::.    .:=*%%%%%%%%%%%%%%%%#===      
       ===#%%%%%%%%#++#%%%%%#=:.      ..... ..        ... .. ...    .:=#%%%%%*--*%%%%%%%%#===       
         ==*%%%%%%*.:+:*%%%%%%#*=:..                            ..:=*#%%%%%%=.-:.:#%%%%%*==         
          ==+#%%#+:**+%#++#%%%%%%#*=-:.                      .:-=*#%%%%%%%+-##-.--#%%%#+==          
           ===*#%%%%%%=.+#:+%%%%%%%%%#*+=--::...    ...::--=+*#%%%%%%%%#-:**:*#**#%%#*===           
             ===##%%%%-=#=.*#=-=#%%%%%%%%%%%###******###%%%%%%%%%%%#=:#%#=.=:+%%%%##===              
               ===#%%%%#++#*.=%+.#*+#%%%%%%%%%%%%%%%%%%%%%%%%%%#:::+#::#####%%%%%#===                
                 ===*#%%%%%#:++.-##::+#=:-*#%%*#%%%%%%#*#%%%%%#:-##**#::+#%%%%#*===                  
                   ===+#%%%%#**#%%=.*%#::::##+.*%#.=%%*.+%%%%%%+.-=:*%%%%%%%#+===                    
                      ===+##%%%%%#*+%%*.+::#*::-##:=%%#.-##%%%%%%%%%%%%%##+===                       
                         ====*%%%%%%%%#####*-**.+%:..+#==+*#%%%%%%%%%%*====                          
                            ====+*##%%%%%%%%%%%%%%%%%%%%%%%%%%%%##*+====                             
                                ======+**###%%%%%%%%%%%%###**+======                                 
                                       ======================                                        
*/
const int maxn = 2000;
int dp[2005][2005];
using namespace std;

void init()
{
    ios_base::sync_with_stdio(0);
    cin.tie(0);
    cout.tie(0);
}

int main()
{
    init();
    string s;
    cin >> s;
    int ans = INT_MIN;
    int n = s.size();    
    for(int i = 1; i <= n; i++)
    {
        dp[i][i] = 1;
    }
    for(int i = 1; i <= n -1; i++)
    {
        if(s[i] == s[i+1]) dp[i][i+1] = 2;
    }
    for(int j = 2; j <= n; j++)
    {
        for(int i = j-2; i >= 0; i--)
        {
            if(s[i] == s[j] ) dp[i][j] = dp[i+1][j-1]+2;
            else dp[i][j] = max(dp[i+1][j], dp[i][j-1]);
        }
    }
    cout << dp[1][n];
    return 0;
}