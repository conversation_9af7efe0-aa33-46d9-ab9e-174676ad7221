#include <bits/stdc++.h>
using namespace std;

int main() {
    cout << R"(
                          .-'''-.
                       .-'       `-.
                     .'             `.
                    /                 \
                   /   .-'''-._.-'''-.   \
                  ;   /  .-"""""-.  \  ;  ;
                 ;   ;  /  .--.  \  ;  ;  ;
                 |   | :  /    \  ; :  |  |
                 |   | | :      : | |  |  |
                 ;   ; \  \____/  / /  ;  ;
                  \   \ '. `----' .' /  / /
                   '.  `-._`.___.'_.'  .'`
                     '-.   `'---'`   .-'
                         `-._   _.-'
                              '''
             _________CHELSEA FOOTBALL CLUB_________
          .-''-.     .-''-.     .-''-.     .-''-.     .-''-.
        .'  ____'. .'  ____'. .'  ____'. .'  ____'. .'  ____'.
       /   /    \  /   /    \ /   /    \ /   /    \ /   /    \
      ;   ;  _   ; ;   ;  _   ;   ;  _   ;   ;  _   ;   ;  _   ;
      |   | ( )  | |   | ( )  |   | ( )  |   | ( )  |   | ( )  |
      ;   ;  `-' ; ;   ;  `-' ;   ;  `-' ;   ;  `-' ;   ;  `-' ;
       \   \    /  \   \    / \   \    / \   \    / \   \    /
        '.  `'`.'    '.  `'`.'   '.  `'`.'   '.  `'`.'   '.  `.'
          '-.____.-'    '-.____.-'   '-.____.-'   '-.____.-' 

                     .--.              .--.
                    /    \            /    \
                   /______\----------/______\
                  /  .--.  \  .--.   /  .--.  \
                 /  /    \  \/    \ /  /    \  \
                ;  ;      ;  ;     ; ;  ;      ; ;
                |  |  /\  |  | /\  | |  |  /\  | |
                |  | /  \ |  |/  \ | |  | /  \ | |
                ;  ; \__/ ;  ;\__/ ; ;  ; \__/ ; ;
                 \  \    / \  \    / /  \    /  /
                  '--'--'   '--'--'  '--'--'--'

                 ________  __________  ________ 
                /  _____/ /  ________\/  ____  \
               /  /  __  /  /  __  __/  / __\  \
              /  /__/ / /  /__/ / /  /  /_/  \  \
              \______/  \______\/_/   \______/__/

                 ____   _    _  _    _  _____  _____
                / ___| | |  | || |  | || ____||  ___|
               | |     | |  | || |  | ||  _|  | |__
               | |___  | |__| || |__| || |___ |  __|
                \____|  \____/  \____/ |_____||_|

           .------------------------------------------------.
          /   .-----------------------------------------.    \
         /   /   .-----------------------------------.   \    \
        /   /   /  .--.   .----.   .--.   .----.     \   \    \
       ;   ;   ;  /    \ /      \ /    \ /      \     ;   ;    ;
       |   |   | ;  ()  |  ()()  |  ()  |  ()()  |    |   |    |
       ;   ;   ;  \    / \      / \    / \      /     ;   ;    ;
        \   \   \  '--'   '----'   '--'   '----'      /   /    /
         '.  '.  '.                                .'  .'   .'
           '-._'----._                      _ _.-'_.-'_.-'
                 `'-._ `'--._________.--'` _.-'`.-'`
                       `'-._        _.-'`.-'`
                             `'----'`

    )" << endl;
    return 0;
}