#include <bits/stdc++.h>
#define endl '\n'
using namespace std;

void init()
{
    ios_base::sync_with_stdio(0);
    cin.tie(0);
    cout.tie(0);
}

int main()
{
    init();
    int t;
    cin >> t;
    while(t--)
    {
        int n,k;
        cin >> n >> k;
        int cnt = 0;
        set<int> s;
        for(int i = 1; i <= n; i++)
        {
            int x;
            cin >> x;
            if(x < k) s.insert(x);
            else if(x == k) cnt++;
        }
        int cnt2 = k - s.size();
        cout << max(cnt, cnt2) << endl;
    }
    return 0;
}