#include <bits/stdc++.h>
#define endl '\n'
using namespace std;
using ll = long long;

bool check(ll t, ll h, ll d) {
    if (t < d) return false;

    ll rests = t - d;
    ll num_blocks = rests + 1;

    ll q = d / num_blocks;
    ll rem = d % num_blocks;

    __int128 cost_small = (__int128)q * (q + 1) / 2;
    __int128 cost_big = (__int128)(q + 1) * (q + 2) / 2;

    ll num_small = num_blocks - rem;
    ll num_big = rem;

    __int128 mx = 0;

    if (num_blocks > 0) mx = max(mx, cost_small);

    if (num_small > 0) {
        __int128 s = (__int128)num_small * cost_small;
        mx = max(mx, s - (num_small - 1));
    }

    if (num_big > 0) {
        __int128 s = (__int128)num_small * cost_small;
        __int128 s2 = s + cost_big;
        mx = max(mx, s2 - num_small);
    }

    if (num_blocks > 0) {
        __int128 total = (__int128)num_small * cost_small + (__int128)num_big * cost_big;
        mx = max(mx, total - rests);
    }

    return h > mx;
}

void solve() {
    ll h, d;
    cin >> h >> d;

    ll l = d, r = 2 * d + h, ans = r;
    while (l <= r) {
        ll mid = (l + r) >> 1;
        if (check(mid, h, d)) {
            ans = mid;
            r = mid - 1;
        } else l = mid + 1;
    }
    cout << ans << endl;
}

int main() {
    ios::sync_with_stdio(0);
    cin.tie(0);

    int t; 
    cin >> t;
    while (t--) solve();
}
