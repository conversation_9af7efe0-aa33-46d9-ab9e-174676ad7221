#include <bits/stdc++.h>
#define endl '\n'
/*                                   ======================                                       
                                ======++*###%%%%%%%%%%%%###*++======                                
                            =====+##%%###*+=*%%%+::*%%%%%%####%%##+=====                            
                         ====*%%%%%%%%=..-=*#%%%=..*%%%%%*. ..:#%%%%%%*====                         
                      ===+##%%%+..=#%%*..=*++#%%=..#%%%%#- .**-+%%%#=+#%##+===                      
                   ===+#%%#=:-##=..+%%#: .-=*#%%=..#%%%%%#-..:*#%%#: ..:=#%%#+===                   
                 ===*#%%%%%*. -+-. .*%#= .*#**%%=..===*#+-##: :#%#-..*%#+#%%%%#*===                 
               ===##%#**#%%%+..:*#: :#%*...:-+#%*****+*%*:....+%#-..-..=%%%%%%%%##===               
             ===*##=.:==*%%%#=..+#*::+%##%%%###******###%%####%%=..+%###%%%%%#+-=##*===             
           ===*#%%+.=#%%%#**%#=.:*%%%##*=--::...    ...::--=*##%#*=:.=#%%#*=:..:#%%%#*===           
          ==+#%%%%=.:#%%%%-:#%%%%#*=-:.                      .:-=*#%###*:..:-..*%%%%%%#+==          
         ==*%%%%%%#:..=*#=.=%%#*=:..                            ..:=*###+##=. -#%%%%%%%%*==         
       ===#%%%%%%%%#+:...-*##+-.                      .             .-+#%%#- :#%%%%%%%%%%#===       
      ===#%%%%%%%%%%%%%%%%*=:.  .:+*+-..           .-##:              .:=*%#=+%%%%%%%%%%%%#===      
     ===%%%%%%%%%%%%%%%%#=:.   :*#%%%%%=         .-=*#%%#*-.            .:=#%%%%%%%%%%%%%%%%===     
    ===#%%%%%%%%%%%%%%#+-.    .=#%*:-*%%-.      .=*#%%%%%%%%%#=.          .-+#%%%%%%%%%%%%%%#===    
    ==#%%%%%%%%%%%%%%#=.      .=#%#++*%#::        =#%%%%#*#%%%#-..          .=#%%%%%%%%%%%%%%#==    
   ==#%%%%%%%%%%%%%%#=.         -#%%#%*-:.    =--*#%%%%%%%###*::.:           .=#%%%%%%%%%%%%%%#==   
  ==+%%%%%%%%%%%%%%*-.          .-*##-::..    -*##%%%%%%%%+::.+**..:-:        .-*%%%%%%%%%%%%%%+==  
  ==#%#+-=--=#%%%%#=.           .*###*-.      .=*#%%%%%%%%%%-...-####%#+.      .=#%%%%#=--=-+#%#==  
 ==*%#-+**=-=-+%%#=.            .**##%%%*-:...=+#%%%%%%%*:::::  -.:-+#%%=.      .=#%%+-=-=**+-#%*== 
 ==#%=-**+==++-##+:             .*##%%%%%%%#+++#%%%%%%%%*:.. .   ..-+#%#=:       :+##=++==+**-=%#== 
==+%%*:**+=+++-%#-.             ..=#%%%%%%%%#++#%%%%%%%%##+*=   .:+*#%#-:.       .-#%-+++=+**:*%%+==
==#%%%*--==--+%%+:                :**:::+#%%###%%%%%%%%%%%%%+:.:+*#%*-::..        :+%%+-===--*%%%#==
==%%%%%%%%#%%%%#=.                :**:.:*#%#=*%%%%%%%%%%#+=:::-+#%+:::.           .=#%%%%#%%%%%%%%==
=+%%%%%%%%%%%%%*-.                :**:. .::-+++*#%%%%%%%#-.  .+##*%%%%%*.         .-#%%%%%%%%%%%%%+=
=+%%%%%%%%%%%%%*-.                :**:. :=++*#%%%%%%%%%%%*:. .+*%%#*++#%*.        .-*%%%%%%%%%%%%%+=
=+%%%%%%%%%%%%%*-                 :**-=+**#%%%%%%%%%%%%%%%-.  .+*#+:::+#-:.        -*%%%%%%%%%%%%%+=
=+%%%%%%%%%%%%%*-.              .+####%%%%%%%%%%##%%#*#%%%+.    -+#=..-::.        .-*%%%%%%%%%%%%%+=
=+%%%%%%%%%%%%%*-.              +*%%%%%%%%%%%%*-:-%%#+*#%%*:.   .:+#*....         .-*%%%%%%%%%%%%%+=
==%%%%%%*++*#%%#-.              .-*#%%*===#*=:::-=+++*#%%%#+.     =*%+..          .=#%%%*+++#%%%%%==
==#%%%+-:***==*%+:                :**=::.....:=++**#%%%%%%%%*-...-+*%*:.          :+%#+:***=:-#%%#==
==+%%++*+:.:**=##-.               :**::.   .=+*#%%%%%%%%%%%+####*##%#-:.         .-#%=**=:.=**=#%+==
 ==#%=--:+*:=*+*#+:               :**:.   .=*#%%%%%%%%%%%%%%+.:----:::..         :+##-**:+*:---##== 
 ==*%*-*+=-+*+=%%#=.              :**:.   :+*%%%%%#+=-+*#%%%%#=...:..           .=#%%%-**+-+**-#*== 
  ==#%#+=--+=*%%%%#=.            .+*#=.    .=+#%%+::...=+*###%%%#=...          .=#%%%%#+=+:==#%#==  
  ==+%%%%%%%%%%%%%%*-.           .=##-:.    -+#%%#-.    .-=+*#%%+::.          .-*%%%%%%%%%%%%%%+==  
   ==#%%%%%%%%%%%%%%#=.           +*#-..   .+#%%%#=-..    .=*#%#::.          .=#%%%%%%%%%%%%%%#==   
    ==#%%%%%%%%%%%%%%#=.           .:-=---=*#%%%*::..     .+#%%+:.          .=#%%%%%%%%%%%%%%#==    
    ===#%%%%%%%%%%%%%%#+-.          +#%%%%%%%%%%-:.  .=*####%%%+:.        .-+#%%%%%%%%%%%%%%#===    
     ==+%%%%%%%%%%%%%%%%#=:.        -####%##%#*-:.   =%%%%%%%%%+:.      .:=#%%%%%%%%%%%%%%%%+==     
      ==+#%%%%%%%%%%%%%%%%*=:.      .:::+::-=::...   .+-:++:==:::.    .:=*%%%%%%%%%%%%%%%%#===      
       ===#%%%%%%%%#++#%%%%%#=:.      ..... ..        ... .. ...    .:=#%%%%%*--*%%%%%%%%#===       
         ==*%%%%%%*.:+:*%%%%%%#*=:..                            ..:=*#%%%%%%=.-:.:#%%%%%*==         
          ==+#%%#+:**+%#++#%%%%%%#*=-:.                      .:-=*#%%%%%%%+-##-.--#%%%#+==          
           ===*#%%%%%%=.+#:+%%%%%%%%%#*+=--::...    ...::--=+*#%%%%%%%%#-:**:*#**#%%#*===           
             ===##%%%%-=#=.*#=-=#%%%%%%%%%%%###******###%%%%%%%%%%%#=:#%#=.=:+%%%%##===              
               ===#%%%%#++#*.=%+.#*+#%%%%%%%%%%%%%%%%%%%%%%%%%%#:::+#::#####%%%%%#===                
                 ===*#%%%%%#:++.-##::+#=:-*#%%*#%%%%%%#*#%%%%%#:-##**#::+#%%%%#*===                  
                   ===+#%%%%#**#%%=.*%#::::##+.*%#.=%%*.+%%%%%%+.-=:*%%%%%%%#+===                    
                      ===+##%%%%%#*+%%*.+::#*::-##:=%%#.-##%%%%%%%%%%%%%##+===                       
                         ====*%%%%%%%%#####*-**.+%:..+#==+*#%%%%%%%%%%*====                          
                            ====+*##%%%%%%%%%%%%%%%%%%%%%%%%%%%%##*+====                             
                                ======+**###%%%%%%%%%%%%###**+======                                 
                                       ======================                                        
*/
using namespace std;

int main() {
    ios::sync_with_stdio(0);
    cin.tie(0);

    int n;
    long long K;
    cin >> n >> K;

    vector<long long> x(n+1), pref(n+1, 0);
    for (int i = 1; i <= n; i++) {
        cin >> x[i];
        pref[i] = pref[i-1] + x[i];
    }

    long long ans = 0;
    for (int l = 1; l <= n; l++) {
        long long target = pref[l-1] + K;
        int r = upper_bound(pref.begin()+l, pref.end(), target) - pref.begin() - 1;
        ans += (r - (l-1));
    }

    cout << ans << "\n";
}
