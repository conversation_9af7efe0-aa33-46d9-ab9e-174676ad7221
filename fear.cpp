#include <bits/stdc++.h>
#define endl '\n'
const int maxn = 1e6;
bool isprime[maxn+5];
int countt[maxn+5];
/*                                   ======================                                       
                                ======++*###%%%%%%%%%%%%###*++======                                
                            =====+##%%###*+=*%%%+::*%%%%%%####%%##+=====                            
                         ====*%%%%%%%%=..-=*#%%%=..*%%%%%*. ..:#%%%%%%*====                         
                      ===+##%%%+..=#%%*..=*++#%%=..#%%%%#- .**-+%%%#=+#%##+===                      
                   ===+#%%#=:-##=..+%%#: .-=*#%%=..#%%%%%#-..:*#%%#: ..:=#%%#+===                   
                 ===*#%%%%%*. -+-. .*%#= .*#**%%=..===*#+-##: :#%#-..*%#+#%%%%#*===                 
               ===##%#**#%%%+..:*#: :#%*...:-+#%*****+*%*:....+%#-..-..=%%%%%%%%##===               
             ===*##=.:==*%%%#=..+#*::+%##%%%###******###%%####%%=..+%###%%%%%#+-=##*===             
           ===*#%%+.=#%%%#**%#=.:*%%%##*=--::...    ...::--=*##%#*=:.=#%%#*=:..:#%%%#*===           
          ==+#%%%%=.:#%%%%-:#%%%%#*=-:.                      .:-=*#%###*:..:-..*%%%%%%#+==          
         ==*%%%%%%#:..=*#=.=%%#*=:..                            ..:=*###+##=. -#%%%%%%%%*==         
       ===#%%%%%%%%#+:...-*##+-.                      .             .-+#%%#- :#%%%%%%%%%%#===       
      ===#%%%%%%%%%%%%%%%%*=:.  .:+*+-..           .-##:              .:=*%#=+%%%%%%%%%%%%#===      
     ===%%%%%%%%%%%%%%%%#=:.   :*#%%%%%=         .-=*#%%#*-.            .:=#%%%%%%%%%%%%%%%%===     
    ===#%%%%%%%%%%%%%%#+-.    .=#%*:-*%%-.      .=*#%%%%%%%%%#=.          .-+#%%%%%%%%%%%%%%#===    
    ==#%%%%%%%%%%%%%%#=.      .=#%#++*%#::        =#%%%%#*#%%%#-..          .=#%%%%%%%%%%%%%%#==    
   ==#%%%%%%%%%%%%%%#=.         -#%%#%*-:.    =--*#%%%%%%%###*::.:           .=#%%%%%%%%%%%%%%#==   
  ==+%%%%%%%%%%%%%%*-.          .-*##-::..    -*##%%%%%%%%+::.+**..:-:        .-*%%%%%%%%%%%%%%+==  
  ==#%#+-=--=#%%%%#=.           .*###*-.      .=*#%%%%%%%%%%-...-####%#+.      .=#%%%%#=--=-+#%#==  
 ==*%#-+**=-=-+%%#=.            .**##%%%*-:...=+#%%%%%%%*:::::  -.:-+#%%=.      .=#%%+-=-=**+-#%*== 
 ==#%=-**+==++-##+:             .*##%%%%%%%#+++#%%%%%%%%*:.. .   ..-+#%#=:       :+##=++==+**-=%#== 
==+%%*:**+=+++-%#-.             ..=#%%%%%%%%#++#%%%%%%%%##+*=   .:+*#%#-:.       .-#%-+++=+**:*%%+==
==#%%%*--==--+%%+:                :**:::+#%%###%%%%%%%%%%%%%+:.:+*#%*-::..        :+%%+-===--*%%%#==
==%%%%%%%%#%%%%#=.                :**:.:*#%#=*%%%%%%%%%%#+=:::-+#%+:::.           .=#%%%%#%%%%%%%%==
=+%%%%%%%%%%%%%*-.                :**:. .::-+++*#%%%%%%%#-.  .+##*%%%%%*.         .-#%%%%%%%%%%%%%+=
=+%%%%%%%%%%%%%*-.                :**:. :=++*#%%%%%%%%%%%*:. .+*%%#*++#%*.        .-*%%%%%%%%%%%%%+=
=+%%%%%%%%%%%%%*-                 :**-=+**#%%%%%%%%%%%%%%%-.  .+*#+:::+#-:.        -*%%%%%%%%%%%%%+=
=+%%%%%%%%%%%%%*-.              .+####%%%%%%%%%%##%%#*#%%%+.    -+#=..-::.        .-*%%%%%%%%%%%%%+=
=+%%%%%%%%%%%%%*-.              +*%%%%%%%%%%%%*-:-%%#+*#%%*:.   .:+#*....         .-*%%%%%%%%%%%%%+=
==%%%%%%*++*#%%#-.              .-*#%%*===#*=:::-=+++*#%%%#+.     =*%+..          .=#%%%*+++#%%%%%==
==#%%%+-:***==*%+:                :**=::.....:=++**#%%%%%%%%*-...-+*%*:.          :+%#+:***=:-#%%#==
==+%%++*+:.:**=##-.               :**::.   .=+*#%%%%%%%%%%%+####*##%#-:.         .-#%=**=:.=**=#%+==
 ==#%=--:+*:=*+*#+:               :**:.   .=*#%%%%%%%%%%%%%%+.:----:::..         :+##-**:+*:---##== 
 ==*%*-*+=-+*+=%%#=.              :**:.   :+*%%%%%#+=-+*#%%%%#=...:..           .=#%%%-**+-+**-#*== 
  ==#%#+=--+=*%%%%#=.            .+*#=.    .=+#%%+::...=+*###%%%#=...          .=#%%%%#+=+:==#%#==  
  ==+%%%%%%%%%%%%%%*-.           .=##-:.    -+#%%#-.    .-=+*#%%+::.          .-*%%%%%%%%%%%%%%+==  
   ==#%%%%%%%%%%%%%%#=.           +*#-..   .+#%%%#=-..    .=*#%#::.          .=#%%%%%%%%%%%%%%#==   
    ==#%%%%%%%%%%%%%%#=.           .:-=---=*#%%%*::..     .+#%%+:.          .=#%%%%%%%%%%%%%%#==    
    ===#%%%%%%%%%%%%%%#+-.          +#%%%%%%%%%%-:.  .=*####%%%+:.        .-+#%%%%%%%%%%%%%%#===    
     ==+%%%%%%%%%%%%%%%%#=:.        -####%##%#*-:.   =%%%%%%%%%+:.      .:=#%%%%%%%%%%%%%%%%+==     
      ==+#%%%%%%%%%%%%%%%%*=:.      .:::+::-=::...   .+-:++:==:::.    .:=*%%%%%%%%%%%%%%%%#===      
       ===#%%%%%%%%#++#%%%%%#=:.      ..... ..        ... .. ...    .:=#%%%%%*--*%%%%%%%%#===       
         ==*%%%%%%*.:+:*%%%%%%#*=:..                            ..:=*#%%%%%%=.-:.:#%%%%%*==         
          ==+#%%#+:**+%#++#%%%%%%#*=-:.                      .:-=*#%%%%%%%+-##-.--#%%%#+==          
           ===*#%%%%%%=.+#:+%%%%%%%%%#*+=--::...    ...::--=+*#%%%%%%%%#-:**:*#**#%%#*===           
             ===##%%%%-=#=.*#=-=#%%%%%%%%%%%###******###%%%%%%%%%%%#=:#%#=.=:+%%%%##===              
               ===#%%%%#++#*.=%+.#*+#%%%%%%%%%%%%%%%%%%%%%%%%%%#:::+#::#####%%%%%#===                
                 ===*#%%%%%#:++.-##::+#=:-*#%%*#%%%%%%#*#%%%%%#:-##**#::+#%%%%#*===                  
                   ===+#%%%%#**#%%=.*%#::::##+.*%#.=%%*.+%%%%%%+.-=:*%%%%%%%#+===                    
                      ===+##%%%%%#*+%%*.+::#*::-##:=%%#.-##%%%%%%%%%%%%%##+===                       
                         ====*%%%%%%%%#####*-**.+%:..+#==+*#%%%%%%%%%%*====                          
                            ====+*##%%%%%%%%%%%%%%%%%%%%%%%%%%%%##*+====                             
                                ======+**###%%%%%%%%%%%%###**+======                                 
                                       ======================                                        
*/

using namespace std;

void sieve(int limit = maxn) {
    fill(isprime, isprime + limit, true);
    isprime[0] = isprime[1] = false;
    for (int i = 2; i * i < limit; i++) {
        if (isprime[i]) {
            for (int j = i * i; j < limit; j += i)
                isprime[j] = false;
        }
    }
}

bool check1(int n) {
    while (n > 0) {
        if (n % 10 == 0) return false;
        n /= 10;
    }
    return true;
}

bool check(int n) {
    if (!isprime[n] || !check1(n)) return false;
    while (n > 0) {
        if (!isprime[n]) return false;
        int digits = log10(n);
        n %= (int)pow(10, digits);
    }
    return true;
}

void demprime() {
    sieve();
    for (int i = 1; i < maxn; i++) {
        countt[i] = countt[i - 1] + (check(i) ? 1 : 0);
    }
}

void init() {
    ios_base::sync_with_stdio(0);
    cin.tie(0);
    cout.tie(0);
}

int main() {
    init();
    demprime();
    int t;
    cin >> t;
    while (t--) {
        int n;
        cin >> n;
        cout << countt[n] << '\n';
    }
    return 0;
}