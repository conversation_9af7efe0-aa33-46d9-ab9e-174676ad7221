#include <bits/stdc++.h>
using namespace std;

void init() {
    ios_base::sync_with_stdio(0);
    cin.tie(0);
}

int main() {
    init();
    int t;
    cin >> t;
    while (t--) 
    {
        int n;
        cin >> n;
        vector<int> a(n + 1);
        for (int i = 1; i <= n; i++) cin >> a[i];

        string s;
        cin >> s;
        s = "0" + s;

        int q;
        cin >> q;

        vector<int> pref(n + 1, 0);
        vector<int> ans(2, 0);

        for (int i = 1; i <= n; i++) 
        
        {
            ans[s[i] - '0'] ^= a[i];
            pref[i] = pref[i - 1] ^ a[i];
        }

        int massxor = 0;
        while (q--)
         {
            int tp;
            cin >> tp;
            if (tp == 1) 
            {
                int l, r;
                cin >> l >> r;
                massxor ^= pref[r] ^ pref[l - 1];
            } 
            else 
            {
                int g;
                cin >> g;
                cout << (massxor ^ ans[g]) << " ";
            }
        }
        cout << "\n";
    }
    return 0;
}
