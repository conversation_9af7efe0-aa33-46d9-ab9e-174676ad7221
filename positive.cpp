#include <bits/stdc++.h>
#define endl '\n'
using namespace std;

void init()
{
    ios_base::sync_with_stdio(0);
    cin.tie(0);
    cout.tie(0);
}

int main()
{
    init();
    int t;
    cin >> t;
    while(t--)
    {
            int n;
        cin >> n;
        int count1 = 0;
        int count0 = 0;
        int count2 = 0;
        for(int i = 1; i <= n; i++)
        {
            int x;
            cin >> x;
            if(x == -1) count2++;
            else if (x == 0) count0++;
            else count1++;
        }
        cout << (count2 % 2) * 2 + count0*1 << endl;
    }
    return 0;
}